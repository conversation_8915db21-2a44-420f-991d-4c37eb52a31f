import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Typography,
  Box,
  Chip,
  Divider,
  Card,
  CardContent,
  Avatar
} from '@mui/material';
import {
  ShoppingCart,
  AttachMoney,
  Person,
  CalendarToday,
  Payment,
  Receipt,
  Inventory
} from '@mui/icons-material';
import { useData } from '../../contexts/DataContext';

const SaleDetailsDialog = ({ open, sale, onClose }) => {
  const { getProduct } = useData();

  if (!sale) return null;

  const product = getProduct(sale.productId);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getPaymentMethodColor = (method) => {
    switch (method) {
      case 'Espèces':
        return 'success';
      case 'Carte':
        return 'primary';
      case 'Mobile Money':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getPaymentMethodIcon = (method) => {
    switch (method) {
      case 'Espèces':
        return '💵';
      case 'Carte':
        return '💳';
      case 'Mobile Money':
        return '📱';
      default:
        return '💰';
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Receipt sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h5" component="div">
              Détails de la Vente #{sale.id}
            </Typography>
          </Box>
          <Chip
            label={`Total: ${formatCurrency(sale.totalPrice)}`}
            color="success"
            size="large"
            icon={<AttachMoney />}
          />
        </Box>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={3}>
          {/* Customer Information */}
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <Person />
                  </Avatar>
                  <Box>
                    <Typography variant="h6">Informations Client</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Détails du client
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="h5" gutterBottom>
                  {sale.customerName || 'Client anonyme'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Sale Information */}
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                    <CalendarToday />
                  </Avatar>
                  <Box>
                    <Typography variant="h6">Date de Vente</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Horodatage de la transaction
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="h6">
                  {new Date(sale.date).toLocaleDateString('fr-FR', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {new Date(sale.date).toLocaleTimeString('fr-FR', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                  })}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Product Information */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Produit Vendu
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Card>
              <CardContent>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} md={2}>
                    <Box
                      sx={{
                        width: 80,
                        height: 80,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#f5f5f5',
                        borderRadius: 1,
                        backgroundImage: product?.image ? `url(${product.image})` : 'none',
                        backgroundSize: 'cover',
                        backgroundPosition: 'center'
                      }}
                    >
                      {!product?.image && (
                        <Inventory sx={{ fontSize: 40, color: 'text.secondary' }} />
                      )}
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <Typography variant="h6" gutterBottom>
                      {sale.productName}
                    </Typography>
                    {product && (
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          SKU: {product.sku}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Description: {product.description || 'Aucune description'}
                        </Typography>
                      </Box>
                    )}
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Box sx={{ textAlign: 'right' }}>
                      <Typography variant="body2" color="text.secondary">
                        Prix unitaire
                      </Typography>
                      <Typography variant="h6">
                        {formatCurrency(sale.unitPrice)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        Quantité: {sale.quantity}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Payment Information */}
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                    <Payment />
                  </Avatar>
                  <Box>
                    <Typography variant="h6">Mode de Paiement</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Méthode utilisée
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="h6" sx={{ mr: 1 }}>
                    {getPaymentMethodIcon(sale.paymentMethod)}
                  </Typography>
                  <Chip
                    label={sale.paymentMethod}
                    color={getPaymentMethodColor(sale.paymentMethod)}
                    variant="outlined"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Total Calculation */}
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                    <ShoppingCart />
                  </Avatar>
                  <Box>
                    <Typography variant="h6">Calcul Total</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Détail du montant
                    </Typography>
                  </Box>
                </Box>
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">
                      {formatCurrency(sale.unitPrice)} × {sale.quantity}
                    </Typography>
                    <Typography variant="body2">
                      {formatCurrency(sale.unitPrice * sale.quantity)}
                    </Typography>
                  </Box>
                  <Divider sx={{ my: 1 }} />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="h6">Total:</Typography>
                    <Typography variant="h6" color="success.main">
                      {formatCurrency(sale.totalPrice)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Additional Information */}
          {product && (
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Informations Produit Actuelles
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Stock Actuel
                    </Typography>
                    <Typography variant="h6" color={product.stock <= product.minStock ? 'error.main' : 'text.primary'}>
                      {product.stock}
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Prix Actuel USD
                    </Typography>
                    <Typography variant="h6">
                      {formatCurrency(product.price)}
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Prix Actuel CDF
                    </Typography>
                    <Typography variant="h6">
                      {product.priceCDF?.toLocaleString()} FC
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Stock Minimum
                    </Typography>
                    <Typography variant="h6">
                      {product.minStock}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Grid>
          )}
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="contained">
          Fermer
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SaleDetailsDialog;
