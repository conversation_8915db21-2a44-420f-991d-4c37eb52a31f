# Manuel Utilisateur - Maboutique

Guide complet d'utilisation du système de gestion de boutique Maboutique.

## 🚀 Démarrage Rapide

### Première Connexion
1. Lancer l'application Maboutique
2. Utiliser un des comptes de démonstration :
   - **Super Admin**: `superadmin` / `super123`
   - **Administrateur**: `admin` / `admin123`
   - **Employé**: `employe` / `emp123`

### Interface Principale
L'interface se compose de :
- **Barre latérale** : Navigation entre les sections
- **Barre supérieure** : Informations utilisateur et notifications
- **Zone principale** : Contenu de la section active

## 📊 Tableau de Bord

### Vue d'Ensemble
Le tableau de bord affiche :
- **Revenus du jour/semaine/mois**
- **Nombre total de produits**
- **Alertes de stock faible**
- **Graphique des ventes sur 14 jours**
- **Top 5 des produits les plus vendus**

### Métri<PERSON> Clés
- **Revenus Aujourd'hui** : Ventes de la journée en cours
- **Revenus Semaine** : Ventes de la semaine en cours
- **Revenus Mois** : Ventes du mois en cours
- **Total Produits** : Nombre de produits en inventaire

### Alertes
- **Stock Faible** : Produits avec un stock ≤ stock minimum
- **Notifications** : Badge rouge sur l'icône de notification

## 📦 Gestion des Produits

### Ajouter un Produit
1. Cliquer sur **"Nouveau Produit"**
2. Remplir les informations :
   - **Nom** : Nom du produit (requis)
   - **Description** : Description détaillée
   - **Prix USD** : Prix en dollars américains (requis)
   - **Prix CDF** : Prix en francs congolais (auto-calculé)
   - **SKU** : Code produit unique (requis)
   - **Catégorie** : Sélectionner une catégorie (requis)
   - **Stock** : Quantité en inventaire (requis)
   - **Stock Min** : Seuil d'alerte de stock faible
   - **Code-barres** : Code-barres du produit (optionnel)
3. Cliquer sur **"Créer"**

### Modifier un Produit
1. Dans la liste des produits, cliquer sur **⋮** → **"Modifier"**
2. Modifier les informations souhaitées
3. Cliquer sur **"Modifier"**

### Supprimer un Produit
⚠️ **Réservé aux Super Administrateurs**
1. Cliquer sur **⋮** → **"Supprimer"**
2. Confirmer la suppression

### Recherche et Filtres
- **Barre de recherche** : Rechercher par nom ou SKU
- **Filtre catégorie** : Filtrer par catégorie de produit
- **Bouton Réinitialiser** : Effacer tous les filtres

### Import/Export CSV
#### Importer des Produits
1. Cliquer sur **"Importer CSV"**
2. Télécharger le modèle CSV
3. Remplir le fichier avec vos données
4. Sélectionner le fichier rempli
5. Cliquer sur **"Importer"**

#### Exporter des Produits
1. Cliquer sur **"Exporter CSV"**
2. Le fichier sera téléchargé automatiquement

## 💰 Gestion des Ventes

### Enregistrer une Vente
1. Cliquer sur **"Nouvelle Vente"**
2. Sélectionner le **produit** dans la liste déroulante
3. Saisir la **quantité** vendue
4. Entrer le **nom du client**
5. Choisir le **mode de paiement** :
   - Espèces
   - Carte
   - Mobile Money
6. Vérifier le **résumé de la vente**
7. Cliquer sur **"Enregistrer la Vente"**

### Modifier une Vente
1. Dans la liste des ventes, cliquer sur **⋮** → **"Modifier"**
2. Modifier les informations
3. Cliquer sur **"Modifier"**

### Supprimer une Vente
1. Cliquer sur **⋮** → **"Supprimer"**
2. Confirmer la suppression
3. Le stock sera automatiquement restauré

### Consulter les Détails
1. Cliquer sur **⋮** → **"Voir Détails"**
2. Consulter toutes les informations de la vente

### Filtres des Ventes
- **Recherche** : Par produit, client ou ID de vente
- **Date** : Filtrer par date spécifique
- **Réinitialiser** : Effacer tous les filtres

## 📈 Rapports

### Rapport Quotidien
- **Filtres de date** : Sélectionner une période
- **Métriques** : Revenus total, nombre de ventes, vente moyenne
- **Tableau détaillé** : Ventes par jour
- **Performance produits** : Classement par revenus générés

### Exporter un Rapport
1. Configurer les filtres de date
2. Cliquer sur **"Exporter CSV"**
3. Le rapport sera téléchargé

### Imprimer un Rapport
1. Cliquer sur **"Imprimer"**
2. Utiliser la fonction d'impression du navigateur

## 📊 Analyses

### Métriques Disponibles
- **Produits Actifs** : Produits avec stock > 0
- **Alertes Stock** : Nombre de produits en stock faible
- **Meilleur Produit** : Produit le plus vendu
- **Valeur Stock** : Valeur totale de l'inventaire

### Graphiques
- **Tendance des Ventes** : Évolution sur la période sélectionnée
- **Modes de Paiement** : Répartition par type de paiement
- **Top 5 Produits** : Produits les plus vendus
- **Performance par Catégorie** : Revenus par catégorie

### Période d'Analyse
Sélectionner la période dans le menu déroulant :
- 7 derniers jours
- 14 derniers jours
- 30 derniers jours
- 60 derniers jours

## 👥 Gestion des Utilisateurs

⚠️ **Réservé aux Administrateurs et Super Administrateurs**

### Rôles Utilisateurs

#### Employé
- Consulter le tableau de bord
- Voir les produits
- Enregistrer des ventes
- Consulter les rapports

#### Administrateur
- Toutes les permissions Employé
- Gérer les produits (ajouter, modifier)
- Voir les analyses
- Gérer les utilisateurs

#### Super Administrateur
- Toutes les permissions Administrateur
- Supprimer des produits
- Modifier les enregistrements financiers
- Accès aux paramètres système

### Matrice des Permissions
Consulter le tableau des permissions pour voir les accès de chaque rôle.

## ⚙️ Fonctionnalités Avancées

### Gestion du Stock
- **Alertes automatiques** : Notification quand stock ≤ stock minimum
- **Mise à jour automatique** : Stock réduit lors des ventes
- **Codes couleur** :
  - 🔴 Rouge : Stock épuisé
  - 🟡 Orange : Stock faible
  - 🟢 Vert : Stock suffisant

### Devises Multiples
- **USD** : Dollar américain (devise principale)
- **CDF** : Franc congolais (conversion automatique)
- **Taux de change** : Configurable dans les paramètres

### Codes-barres
- **Génération automatique** : Codes EAN-13
- **Simulation de scan** : Interface visuelle de scan

## 🔧 Paramètres et Configuration

### Personnalisation
- **Taux de change** : Modifier le taux USD/CDF
- **Seuils de stock** : Définir les niveaux d'alerte
- **Catégories** : Ajouter/modifier les catégories

### Sauvegarde des Données
- **Stockage local** : Données sauvées automatiquement
- **Export CSV** : Sauvegarde manuelle des données
- **Réinitialisation** : Effacer toutes les données

## 🚨 Résolution de Problèmes

### Problèmes Courants

#### Données Manquantes
- Actualiser la page (F5)
- Vérifier la connexion
- Redémarrer l'application

#### Erreurs de Calcul
- Vérifier les prix saisis
- Contrôler les quantités
- Vérifier le taux de change

#### Stock Incorrect
- Vérifier les ventes enregistrées
- Contrôler les modifications de produits
- Réinitialiser si nécessaire

### Messages d'Erreur

#### "Stock insuffisant"
- Le stock disponible est inférieur à la quantité demandée
- Vérifier le stock actuel du produit
- Réduire la quantité ou réapprovisionner

#### "Accès non autorisé"
- Votre rôle ne permet pas cette action
- Contacter un administrateur
- Vérifier vos permissions

#### "Données invalides"
- Vérifier les champs requis
- Contrôler le format des données
- Corriger les erreurs signalées

## 📱 Raccourcis Clavier

### Navigation
- **Ctrl+N** : Nouveau produit
- **Ctrl+S** : Nouvelle vente
- **Ctrl+R** : Rapport quotidien
- **Ctrl+I** : Importer CSV
- **Ctrl+E** : Exporter CSV

### Général
- **F5** : Actualiser
- **F11** : Plein écran
- **Ctrl+Q** : Quitter (desktop)

## 💡 Conseils d'Utilisation

### Bonnes Pratiques
1. **Sauvegarder régulièrement** : Exporter les données importantes
2. **Vérifier les stocks** : Contrôler quotidiennement les alertes
3. **Former les utilisateurs** : Assurer une utilisation correcte
4. **Maintenir les données** : Nettoyer régulièrement les données obsolètes

### Optimisation
- **Utiliser les filtres** : Pour trouver rapidement les informations
- **Organiser les catégories** : Classer logiquement les produits
- **Suivre les tendances** : Analyser régulièrement les performances
- **Planifier les réapprovisionnements** : Anticiper les ruptures de stock

---

**Support** : Pour toute question, consulter la documentation technique ou contacter l'équipe de support.
