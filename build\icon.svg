<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1976d2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#42a5f5;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="50" cy="50" r="45" fill="url(#gradient)" stroke="#fff" stroke-width="2"/>
  
  <!-- Store icon -->
  <g fill="#fff">
    <!-- Store building -->
    <rect x="25" y="35" width="50" height="40" rx="2"/>
    
    <!-- Store front -->
    <rect x="30" y="40" width="40" height="30" fill="#e3f2fd"/>
    
    <!-- Door -->
    <rect x="45" y="50" width="10" height="20" fill="#1976d2"/>
    
    <!-- Windows -->
    <rect x="32" y="42" width="8" height="6" fill="#1976d2"/>
    <rect x="60" y="42" width="8" height="6" fill="#1976d2"/>
    
    <!-- Awning -->
    <path d="M20 35 L80 35 L75 30 L25 30 Z" fill="#ff5722"/>
    
    <!-- Sign -->
    <rect x="35" y="25" width="30" height="8" rx="1" fill="#fff"/>
    <text x="50" y="31" text-anchor="middle" font-family="Arial, sans-serif" font-size="6" fill="#1976d2" font-weight="bold">SHOP</text>
  </g>
</svg>
