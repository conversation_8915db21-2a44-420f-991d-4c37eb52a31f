import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  TextField,
  InputAdornment,
  Fab,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Alert
} from '@mui/material';
import {
  Add,
  Search,
  PointOfSale,
  MoreVert,
  Edit,
  Delete,
  Visibility,
  AttachMoney,
  Person,
  CalendarToday
} from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';
import { useData } from '../../contexts/DataContext';
import { useAuth } from '../../contexts/AuthContext';
import SaleDialog from './SaleDialog';
import SaleDetailsDialog from './SaleDetailsDialog';

const Sales = () => {
  const { sales, products, deleteSale, getTotalRevenue } = useData();
  const { hasPermission } = useAuth();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [saleDialog, setSaleDialog] = useState({ open: false, sale: null });
  const [detailsDialog, setDetailsDialog] = useState({ open: false, sale: null });
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedSale, setSelectedSale] = useState(null);

  // Filter sales
  const filteredSales = sales.filter(sale => {
    const matchesSearch = sale.productName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.id.toString().includes(searchTerm);
    
    const matchesDate = !dateFilter || 
                       sale.date.split('T')[0] === dateFilter;
    
    return matchesSearch && matchesDate;
  });

  // Calculate metrics
  const today = new Date();
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const dailyRevenue = getTotalRevenue(startOfDay);
  const totalRevenue = getTotalRevenue();

  // DataGrid columns
  const columns = [
    {
      field: 'id',
      headerName: 'ID Vente',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={`#${params.value}`}
          size="small"
          color="primary"
          variant="outlined"
        />
      )
    },
    {
      field: 'productName',
      headerName: 'Produit',
      flex: 1,
      minWidth: 200
    },
    {
      field: 'customerName',
      headerName: 'Client',
      width: 150,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Person sx={{ mr: 1, fontSize: 16, color: 'text.secondary' }} />
          <Typography variant="body2">
            {params.value || 'Client anonyme'}
          </Typography>
        </Box>
      )
    },
    {
      field: 'quantity',
      headerName: 'Quantité',
      width: 100,
      align: 'center',
      renderCell: (params) => (
        <Chip
          label={params.value}
          size="small"
          color="info"
        />
      )
    },
    {
      field: 'unitPrice',
      headerName: 'Prix Unitaire',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2">
          ${params.value}
        </Typography>
      )
    },
    {
      field: 'totalPrice',
      headerName: 'Total',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2" fontWeight="bold" color="success.main">
          ${params.value}
        </Typography>
      )
    },
    {
      field: 'paymentMethod',
      headerName: 'Paiement',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          size="small"
          color={
            params.value === 'Espèces' ? 'success' :
            params.value === 'Carte' ? 'primary' : 'warning'
          }
          variant="outlined"
        />
      )
    },
    {
      field: 'date',
      headerName: 'Date',
      width: 150,
      renderCell: (params) => (
        <Box>
          <Typography variant="body2">
            {new Date(params.value).toLocaleDateString('fr-FR')}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {new Date(params.value).toLocaleTimeString('fr-FR', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </Typography>
        </Box>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 80,
      sortable: false,
      renderCell: (params) => (
        <IconButton
          size="small"
          onClick={(e) => handleMenuOpen(e, params.row)}
        >
          <MoreVert />
        </IconButton>
      )
    }
  ];

  const handleMenuOpen = (event, sale) => {
    setAnchorEl(event.currentTarget);
    setSelectedSale(sale);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedSale(null);
  };

  const handleAddSale = () => {
    setSaleDialog({ open: true, sale: null });
  };

  const handleEditSale = () => {
    setSaleDialog({ open: true, sale: selectedSale });
    handleMenuClose();
  };

  const handleViewSale = () => {
    setDetailsDialog({ open: true, sale: selectedSale });
    handleMenuClose();
  };

  const handleDeleteSale = () => {
    if (selectedSale && hasPermission('manage_sales')) {
      if (window.confirm(`Êtes-vous sûr de vouloir supprimer la vente #${selectedSale.id} ?`)) {
        deleteSale(selectedSale.id);
      }
    }
    handleMenuClose();
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Gestion des Ventes
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            {sales.length} vente(s) • Revenus du jour: {formatCurrency(dailyRevenue)}
          </Typography>
        </Box>
        {hasPermission('manage_sales') && (
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleAddSale}
          >
            Nouvelle Vente
          </Button>
        )}
      </Box>

      {/* Revenue Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Revenus Aujourd'hui
                  </Typography>
                  <Typography variant="h5" component="div">
                    {formatCurrency(dailyRevenue)}
                  </Typography>
                </Box>
                <AttachMoney sx={{ fontSize: 40, color: 'success.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Revenus Total
                  </Typography>
                  <Typography variant="h5" component="div">
                    {formatCurrency(totalRevenue)}
                  </Typography>
                </Box>
                <PointOfSale sx={{ fontSize: 40, color: 'primary.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Ventes Aujourd'hui
                  </Typography>
                  <Typography variant="h5" component="div">
                    {sales.filter(sale => 
                      sale.date.split('T')[0] === today.toISOString().split('T')[0]
                    ).length}
                  </Typography>
                </Box>
                <CalendarToday sx={{ fontSize: 40, color: 'info.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Total Ventes
                  </Typography>
                  <Typography variant="h5" component="div">
                    {sales.length}
                  </Typography>
                </Box>
                <PointOfSale sx={{ fontSize: 40, color: 'warning.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Rechercher par produit, client ou ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="date"
                label="Filtrer par date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => {
                  setSearchTerm('');
                  setDateFilter('');
                }}
              >
                Réinitialiser
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Sales Table */}
      <Card>
        <Box sx={{ height: 600, width: '100%' }}>
          <DataGrid
            rows={filteredSales}
            columns={columns}
            pageSize={10}
            rowsPerPageOptions={[10, 25, 50]}
            disableSelectionOnClick
            localeText={{
              noRowsLabel: 'Aucune vente trouvée',
              footerRowSelected: (count) => `${count} ligne(s) sélectionnée(s)`,
            }}
          />
        </Box>
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleViewSale}>
          <Visibility sx={{ mr: 1 }} fontSize="small" />
          Voir Détails
        </MenuItem>
        {hasPermission('manage_sales') && (
          <MenuItem onClick={handleEditSale}>
            <Edit sx={{ mr: 1 }} fontSize="small" />
            Modifier
          </MenuItem>
        )}
        {hasPermission('manage_sales') && (
          <MenuItem onClick={handleDeleteSale} sx={{ color: 'error.main' }}>
            <Delete sx={{ mr: 1 }} fontSize="small" />
            Supprimer
          </MenuItem>
        )}
      </Menu>

      {/* Floating Action Button for Mobile */}
      {hasPermission('manage_sales') && (
        <Fab
          color="primary"
          aria-label="add"
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            display: { xs: 'flex', md: 'none' }
          }}
          onClick={handleAddSale}
        >
          <Add />
        </Fab>
      )}

      {/* Dialogs */}
      <SaleDialog
        open={saleDialog.open}
        sale={saleDialog.sale}
        onClose={() => setSaleDialog({ open: false, sale: null })}
      />

      <SaleDetailsDialog
        open={detailsDialog.open}
        sale={detailsDialog.sale}
        onClose={() => setDetailsDialog({ open: false, sale: null })}
      />
    </Box>
  );
};

export default Sales;
