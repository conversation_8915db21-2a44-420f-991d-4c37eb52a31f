import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  Box,
  Typography,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  InputAdornment
} from '@mui/material';
import { Person, AttachMoney, ShoppingCart } from '@mui/icons-material';
import { useData } from '../../contexts/DataContext';

const SaleDialog = ({ open, sale, onClose }) => {
  const { products, addSale, updateSale } = useData();
  const isEdit = Boolean(sale);

  const [formData, setFormData] = useState({
    productId: '',
    customerName: '',
    quantity: '',
    paymentMethod: 'Espèces'
  });

  const [selectedProduct, setSelectedProduct] = useState(null);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const paymentMethods = ['Espèces', 'Carte', 'Mobile Money'];

  useEffect(() => {
    if (sale) {
      setFormData({
        productId: sale.productId?.toString() || '',
        customerName: sale.customerName || '',
        quantity: sale.quantity?.toString() || '',
        paymentMethod: sale.paymentMethod || 'Espèces'
      });
      
      const product = products.find(p => p.id === sale.productId);
      setSelectedProduct(product || null);
    } else {
      setFormData({
        productId: '',
        customerName: '',
        quantity: '',
        paymentMethod: 'Espèces'
      });
      setSelectedProduct(null);
    }
    setErrors({});
  }, [sale, products, open]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleProductChange = (event, newValue) => {
    setSelectedProduct(newValue);
    setFormData(prev => ({
      ...prev,
      productId: newValue ? newValue.id.toString() : ''
    }));

    if (errors.productId) {
      setErrors(prev => ({
        ...prev,
        productId: ''
      }));
    }
  };

  const calculateTotal = () => {
    if (selectedProduct && formData.quantity) {
      const quantity = parseInt(formData.quantity);
      return selectedProduct.price * quantity;
    }
    return 0;
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.productId) {
      newErrors.productId = 'Le produit est requis';
    }

    if (!formData.quantity || isNaN(parseInt(formData.quantity)) || parseInt(formData.quantity) <= 0) {
      newErrors.quantity = 'La quantité doit être un nombre positif';
    }

    if (selectedProduct && formData.quantity) {
      const requestedQuantity = parseInt(formData.quantity);
      const availableStock = selectedProduct.stock;
      
      // For edits, add back the original quantity to available stock
      const adjustedStock = isEdit && sale ? availableStock + sale.quantity : availableStock;
      
      if (requestedQuantity > adjustedStock) {
        newErrors.quantity = `Stock insuffisant (disponible: ${adjustedStock})`;
      }
    }

    if (!formData.customerName.trim()) {
      newErrors.customerName = 'Le nom du client est requis';
    }

    if (!formData.paymentMethod) {
      newErrors.paymentMethod = 'Le mode de paiement est requis';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const quantity = parseInt(formData.quantity);
      const unitPrice = selectedProduct.price;
      const totalPrice = unitPrice * quantity;

      const saleData = {
        productId: selectedProduct.id,
        productName: selectedProduct.name,
        customerName: formData.customerName.trim(),
        quantity,
        unitPrice,
        totalPrice,
        paymentMethod: formData.paymentMethod
      };

      if (isEdit) {
        updateSale(sale.id, saleData);
      } else {
        addSale(saleData);
      }

      onClose();
    } catch (error) {
      console.error('Error saving sale:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {isEdit ? 'Modifier la Vente' : 'Nouvelle Vente'}
      </DialogTitle>
      
      <form onSubmit={handleSubmit}>
        <DialogContent>
          <Grid container spacing={3}>
            {/* Product Selection */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Sélection du Produit
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Autocomplete
                value={selectedProduct}
                onChange={handleProductChange}
                options={products.filter(p => p.stock > 0 || (isEdit && p.id === sale?.productId))}
                getOptionLabel={(option) => `${option.name} (Stock: ${option.stock}) - $${option.price}`}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Produit"
                    error={!!errors.productId}
                    helperText={errors.productId}
                    required
                  />
                )}
                renderOption={(props, option) => (
                  <Box component="li" {...props}>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="body1">{option.name}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        SKU: {option.sku} • Stock: {option.stock} • ${option.price}
                      </Typography>
                    </Box>
                  </Box>
                )}
              />
            </Grid>

            {/* Sale Details */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Détails de la Vente
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Nom du Client"
                name="customerName"
                value={formData.customerName}
                onChange={handleChange}
                error={!!errors.customerName}
                helperText={errors.customerName}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Person />
                    </InputAdornment>
                  ),
                }}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Quantité"
                name="quantity"
                type="number"
                value={formData.quantity}
                onChange={handleChange}
                error={!!errors.quantity}
                helperText={errors.quantity || (selectedProduct ? `Stock disponible: ${selectedProduct.stock}` : '')}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <ShoppingCart />
                    </InputAdornment>
                  ),
                }}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.paymentMethod} required>
                <InputLabel>Mode de Paiement</InputLabel>
                <Select
                  name="paymentMethod"
                  value={formData.paymentMethod}
                  onChange={handleChange}
                  label="Mode de Paiement"
                >
                  {paymentMethods.map((method) => (
                    <MenuItem key={method} value={method}>
                      {method}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Price Summary */}
            {selectedProduct && formData.quantity && (
              <Grid item xs={12}>
                <Box sx={{ p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                  <Typography variant="h6" gutterBottom>
                    Résumé de la Vente
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Prix unitaire:
                      </Typography>
                      <Typography variant="h6">
                        ${selectedProduct.price}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Quantité:
                      </Typography>
                      <Typography variant="h6">
                        {formData.quantity}
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 1, pt: 1, borderTop: '1px solid #ddd' }}>
                        <Typography variant="h6">
                          Total:
                        </Typography>
                        <Typography variant="h4" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                          <AttachMoney />
                          {calculateTotal().toFixed(2)}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </Grid>
            )}

            {/* Stock Warning */}
            {selectedProduct && selectedProduct.stock <= selectedProduct.minStock && (
              <Grid item xs={12}>
                <Alert severity="warning">
                  <strong>Attention:</strong> Ce produit a un stock faible ({selectedProduct.stock} restant).
                </Alert>
              </Grid>
            )}
          </Grid>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose}>
            Annuler
          </Button>
          <Button 
            type="submit" 
            variant="contained"
            disabled={loading || !selectedProduct || !formData.quantity}
          >
            {loading ? 'Enregistrement...' : (isEdit ? 'Modifier' : 'Enregistrer la Vente')}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default SaleDialog;
