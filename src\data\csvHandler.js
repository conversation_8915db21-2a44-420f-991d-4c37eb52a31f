// CSV handling utilities for import/export functionality

export const parseCSV = (csvText) => {
  const lines = csvText.split('\n').filter(line => line.trim());
  if (lines.length === 0) return [];

  const headers = lines[0].split(',').map(header => header.trim().replace(/"/g, ''));
  const data = [];

  for (let i = 1; i < lines.length; i++) {
    const values = parseCSVLine(lines[i]);
    if (values.length === headers.length) {
      const row = {};
      headers.forEach((header, index) => {
        row[header] = values[index];
      });
      data.push(row);
    }
  }

  return data;
};

const parseCSVLine = (line) => {
  const result = [];
  let current = '';
  let inQuotes = false;

  for (let i = 0; i < line.length; i++) {
    const char = line[i];
    
    if (char === '"') {
      inQuotes = !inQuotes;
    } else if (char === ',' && !inQuotes) {
      result.push(current.trim());
      current = '';
    } else {
      current += char;
    }
  }
  
  result.push(current.trim());
  return result;
};

export const generateCSV = (data, headers) => {
  const csvHeaders = headers.join(',');
  const csvRows = data.map(row => 
    headers.map(header => {
      const value = row[header] || '';
      // Escape quotes and wrap in quotes if contains comma or quote
      if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    }).join(',')
  );
  
  return [csvHeaders, ...csvRows].join('\n');
};

export const downloadCSV = (data, filename, headers) => {
  const csvContent = generateCSV(data, headers);
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};

export const validateProductCSV = (data) => {
  const requiredFields = ['Nom', 'Prix USD', 'Prix CDF', 'SKU', 'Catégorie ID', 'Stock', 'Stock Min'];
  const errors = [];
  const validData = [];

  data.forEach((row, index) => {
    const rowErrors = [];
    
    // Check required fields
    requiredFields.forEach(field => {
      if (!row[field] || row[field].toString().trim() === '') {
        rowErrors.push(`${field} est requis`);
      }
    });

    // Validate numeric fields
    if (row['Prix USD'] && isNaN(parseFloat(row['Prix USD']))) {
      rowErrors.push('Prix USD doit être un nombre');
    }
    
    if (row['Prix CDF'] && isNaN(parseFloat(row['Prix CDF']))) {
      rowErrors.push('Prix CDF doit être un nombre');
    }
    
    if (row['Stock'] && isNaN(parseInt(row['Stock']))) {
      rowErrors.push('Stock doit être un nombre entier');
    }
    
    if (row['Stock Min'] && isNaN(parseInt(row['Stock Min']))) {
      rowErrors.push('Stock Min doit être un nombre entier');
    }
    
    if (row['Catégorie ID'] && (isNaN(parseInt(row['Catégorie ID'])) || parseInt(row['Catégorie ID']) < 1 || parseInt(row['Catégorie ID']) > 6)) {
      rowErrors.push('Catégorie ID doit être entre 1 et 6');
    }

    if (rowErrors.length > 0) {
      errors.push({
        row: index + 1,
        errors: rowErrors,
        data: row
      });
    } else {
      validData.push({
        name: row['Nom'].trim(),
        description: row['Description'] ? row['Description'].trim() : '',
        price: parseFloat(row['Prix USD']),
        priceCDF: parseFloat(row['Prix CDF']),
        sku: row['SKU'].trim(),
        categoryId: parseInt(row['Catégorie ID']),
        stock: parseInt(row['Stock']),
        minStock: parseInt(row['Stock Min']),
        barcode: row['Code-barres'] ? row['Code-barres'].trim() : '',
        image: '/images/products/default.jpg'
      });
    }
  });

  return {
    valid: validData,
    errors,
    totalRows: data.length
  };
};

export const generateProductTemplate = () => {
  const template = [
    ['Nom', 'Description', 'Prix USD', 'Prix CDF', 'SKU', 'Catégorie ID', 'Stock', 'Stock Min', 'Code-barres'],
    ['Exemple Produit 1', 'Description du produit exemple', '25.99', '64975', 'EX-001', '1', '50', '10', '1234567890123'],
    ['Exemple Produit 2', 'Autre description', '15.50', '38750', 'EX-002', '2', '30', '5', '2345678901234'],
    ['Exemple Produit 3', '', '8.75', '21875', 'EX-003', '3', '100', '20', '']
  ];
  
  return generateCSV(template.slice(1), template[0]);
};

export const exportSalesData = (sales, products) => {
  const salesData = sales.map(sale => {
    const product = products.find(p => p.id === sale.productId);
    return {
      'ID Vente': sale.id,
      'Date': new Date(sale.date).toLocaleDateString('fr-FR'),
      'Heure': new Date(sale.date).toLocaleTimeString('fr-FR'),
      'Produit': sale.productName,
      'SKU': product ? product.sku : '',
      'Client': sale.customerName,
      'Quantité': sale.quantity,
      'Prix Unitaire USD': sale.unitPrice,
      'Total USD': sale.totalPrice,
      'Mode de Paiement': sale.paymentMethod,
      'Catégorie': product ? getCategoryName(product.categoryId) : ''
    };
  });

  const headers = [
    'ID Vente', 'Date', 'Heure', 'Produit', 'SKU', 'Client', 
    'Quantité', 'Prix Unitaire USD', 'Total USD', 'Mode de Paiement', 'Catégorie'
  ];

  return generateCSV(salesData, headers);
};

export const exportProductsData = (products) => {
  const productsData = products.map(product => ({
    'Nom': product.name,
    'Description': product.description || '',
    'Prix USD': product.price,
    'Prix CDF': product.priceCDF,
    'SKU': product.sku,
    'Catégorie': getCategoryName(product.categoryId),
    'Stock Actuel': product.stock,
    'Stock Minimum': product.minStock,
    'Code-barres': product.barcode || '',
    'Date Création': new Date(product.createdAt).toLocaleDateString('fr-FR'),
    'Dernière Modification': new Date(product.updatedAt).toLocaleDateString('fr-FR')
  }));

  const headers = [
    'Nom', 'Description', 'Prix USD', 'Prix CDF', 'SKU', 'Catégorie',
    'Stock Actuel', 'Stock Minimum', 'Code-barres', 'Date Création', 'Dernière Modification'
  ];

  return generateCSV(productsData, headers);
};

const getCategoryName = (categoryId) => {
  const categories = {
    1: 'Électronique',
    2: 'Vêtements', 
    3: 'Alimentation',
    4: 'Cosmétiques',
    5: 'Maison',
    6: 'Sport'
  };
  return categories[categoryId] || 'Autre';
};
