# Guide d'Installation - Maboutique

Ce guide vous accompagne dans l'installation et la configuration de l'application Maboutique sur votre système.

## 📋 Prérequis Système

### Logiciels Requis
- **Node.js** version 16.0 ou supérieure
- **npm** version 7.0 ou supérieure (inclus avec Node.js)
- **Git** (optionnel, pour cloner le repository)

### Systèmes d'Exploitation Supportés
- Windows 10/11 (x64)
- macOS 10.14 ou supérieur
- Linux (Ubuntu 18.04+, Debian 10+, CentOS 8+)

### Configuration Matérielle Recommandée
- **RAM**: 4 GB minimum, 8 GB recommandé
- **Stockage**: 500 MB d'espace libre
- **Processeur**: Dual-core 2.0 GHz ou supérieur
- **Résolution**: 1280x720 minimum, 1920x1080 recommandé

## 🚀 Installation Rapide

### Étape 1: Télécharger le Projet
```bash
# Option A: Cloner avec Git
git clone <repository-url>
cd Maboutique

# Option B: Télécharger et extraire l'archive ZIP
# Extraire dans un dossier de votre choix
```

### Étape 2: Installer les Dépendances
```bash
# Installer toutes les dépendances
npm install

# Vérifier l'installation
npm list --depth=0
```

### Étape 3: Lancer l'Application
```bash
# Mode développement (recommandé pour les tests)
npm run electron-dev

# Ou lancer uniquement la version web
npm run dev
```

## 🔧 Installation Détaillée

### 1. Vérification des Prérequis

#### Vérifier Node.js
```bash
node --version
# Doit afficher v16.0.0 ou supérieur
```

#### Vérifier npm
```bash
npm --version
# Doit afficher 7.0.0 ou supérieur
```

#### Installer Node.js (si nécessaire)
- Télécharger depuis [nodejs.org](https://nodejs.org/)
- Choisir la version LTS (Long Term Support)
- Suivre l'assistant d'installation

### 2. Configuration du Projet

#### Cloner le Repository
```bash
git clone <repository-url>
cd Maboutique
```

#### Installer les Dépendances
```bash
# Installation complète
npm install

# En cas d'erreur, essayer:
npm install --legacy-peer-deps
```

#### Vérifier l'Installation
```bash
# Lister les packages installés
npm list

# Vérifier les vulnérabilités
npm audit
```

### 3. Configuration Optionnelle

#### Variables d'Environnement
Créer un fichier `.env` dans le dossier racine (optionnel):
```env
# Configuration de développement
REACT_APP_VERSION=1.0.0
REACT_APP_ENV=development

# Configuration de l'API (pour future intégration)
REACT_APP_API_URL=http://localhost:3001/api

# Configuration Electron
ELECTRON_IS_DEV=true
```

#### Configuration du Taux de Change
Modifier le taux USD/CDF dans `src/utils/constants.js`:
```javascript
export const EXCHANGE_RATE = {
  USD_TO_CDF: 2500, // Modifier selon le taux actuel
  CDF_TO_USD: 0.0004
};
```

## 🏃‍♂️ Modes de Lancement

### Mode Développement
```bash
# Application Electron complète (recommandé)
npm run electron-dev

# Application web uniquement
npm run dev
```

### Mode Production
```bash
# Construire l'application
npm run build

# Lancer en mode production
npm run preview
```

### Construction des Exécutables
```bash
# Construire pour la plateforme actuelle
npm run dist

# Construire pour toutes les plateformes
npm run dist-all

# Construire sans créer d'installateur
npm run pack
```

## 🔍 Résolution des Problèmes

### Erreurs Communes

#### Erreur: "Module not found"
```bash
# Supprimer node_modules et réinstaller
rm -rf node_modules package-lock.json
npm install
```

#### Erreur: "Port already in use"
```bash
# Tuer les processus utilisant le port 5173
# Windows:
netstat -ano | findstr :5173
taskkill /PID <PID> /F

# macOS/Linux:
lsof -ti:5173 | xargs kill -9
```

#### Erreur: "Permission denied"
```bash
# Donner les permissions d'exécution (macOS/Linux)
chmod +x node_modules/.bin/*

# Ou utiliser sudo (non recommandé)
sudo npm install
```

#### Erreur Electron: "Failed to load URL"
- Vérifier que le serveur de développement fonctionne
- Attendre que Vite soit complètement démarré
- Vérifier les ports dans `electron/main.js`

### Problèmes de Performance

#### Application Lente
- Fermer les outils de développement
- Vérifier la RAM disponible
- Redémarrer l'application

#### Erreurs de Mémoire
- Augmenter la limite de mémoire Node.js:
```bash
export NODE_OPTIONS="--max-old-space-size=4096"
npm run electron-dev
```

### Problèmes de Données

#### Données Manquantes
- Les données sont stockées dans localStorage
- Ouvrir les outils de développement → Application → Local Storage
- Supprimer les clés `maboutique_*` pour réinitialiser

#### Réinitialiser l'Application
```bash
# Supprimer les données locales
# Dans la console du navigateur:
localStorage.clear();

# Ou redémarrer l'application
```

## 📦 Structure des Fichiers

```
Maboutique/
├── electron/              # Configuration Electron
│   ├── main.js            # Processus principal
│   └── preload.js         # Script de préchargement
├── src/                   # Code source React
│   ├── components/        # Composants React
│   ├── contexts/          # Contextes (Auth, Data)
│   ├── data/             # Gestion des données
│   ├── utils/            # Fonctions utilitaires
│   └── main.jsx          # Point d'entrée React
├── public/               # Assets statiques
├── build/                # Fichiers construits
├── dist/                 # Exécutables générés
├── package.json          # Configuration npm
└── README.md            # Documentation
```

## 🔧 Maintenance

### Mise à Jour des Dépendances
```bash
# Vérifier les mises à jour
npm outdated

# Mettre à jour les packages
npm update

# Mettre à jour npm lui-même
npm install -g npm@latest
```

### Nettoyage
```bash
# Nettoyer les fichiers de build
npm run clean

# Nettoyer le cache npm
npm cache clean --force
```

### Sauvegarde
- Sauvegarder le dossier du projet
- Exporter les données depuis l'application
- Conserver une copie du fichier `package.json`

## 📞 Support

### En Cas de Problème
1. Vérifier cette documentation
2. Consulter les logs dans la console
3. Redémarrer l'application
4. Réinstaller les dépendances
5. Contacter le support technique

### Logs et Débogage
```bash
# Lancer avec logs détaillés
DEBUG=* npm run electron-dev

# Logs Electron
npm run electron-dev --verbose
```

### Informations Système
```bash
# Version Node.js
node --version

# Version npm
npm --version

# Informations système
npm run env
```

---

**Note**: Cette application est un prototype de démonstration. Pour un usage en production, des configurations de sécurité et de performance supplémentaires sont recommandées.
