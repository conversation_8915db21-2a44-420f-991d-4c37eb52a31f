import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Typography,
  Box,
  Chip,
  Divider,
  Card,
  CardContent
} from '@mui/material';
import {
  Inventory,
  AttachMoney,
  QrCode,
  Category,
  CalendarToday,
  Warning
} from '@mui/icons-material';

const ProductDetailsDialog = ({ open, product, onClose }) => {
  if (!product) return null;

  const formatCurrency = (amount, currency = 'USD') => {
    if (currency === 'CDF') {
      return new Intl.NumberFormat('fr-CD', {
        style: 'currency',
        currency: 'CDF'
      }).format(amount);
    }
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getCategoryName = (categoryId) => {
    const categories = {
      1: 'Électronique',
      2: 'Vêtements',
      3: 'Alimentation',
      4: 'Cosmétiques',
      5: 'Maison',
      6: 'Sport'
    };
    return categories[categoryId] || 'Autre';
  };

  const getStockStatus = () => {
    if (product.stock === 0) {
      return { label: 'Épuisé', color: 'error' };
    } else if (product.stock <= product.minStock) {
      return { label: 'Stock Faible', color: 'warning' };
    } else {
      return { label: 'En Stock', color: 'success' };
    }
  };

  const stockStatus = getStockStatus();

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h5" component="div">
            Détails du Produit
          </Typography>
          <Chip
            label={stockStatus.label}
            color={stockStatus.color}
            icon={stockStatus.color === 'warning' ? <Warning /> : <Inventory />}
          />
        </Box>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={3}>
          {/* Product Image */}
          <Grid item xs={12} md={4}>
            <Card>
              <Box
                sx={{
                  height: 200,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#f5f5f5',
                  backgroundImage: product.image ? `url(${product.image})` : 'none',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center'
                }}
              >
                {!product.image && (
                  <Inventory sx={{ fontSize: 60, color: 'text.secondary' }} />
                )}
              </Box>
            </Card>
          </Grid>

          {/* Basic Information */}
          <Grid item xs={12} md={8}>
            <Typography variant="h4" gutterBottom>
              {product.name}
            </Typography>
            
            <Typography variant="body1" color="text.secondary" paragraph>
              {product.description || 'Aucune description disponible'}
            </Typography>

            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <Chip
                label={getCategoryName(product.categoryId)}
                color="primary"
                variant="outlined"
                icon={<Category />}
              />
              <Chip
                label={`SKU: ${product.sku}`}
                variant="outlined"
              />
            </Box>
          </Grid>

          {/* Pricing Information */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Informations de Prix
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <AttachMoney sx={{ mr: 1, color: 'success.main' }} />
                      <Typography variant="subtitle2">Prix USD</Typography>
                    </Box>
                    <Typography variant="h4" color="success.main">
                      {formatCurrency(product.price)}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <AttachMoney sx={{ mr: 1, color: 'info.main' }} />
                      <Typography variant="subtitle2">Prix CDF</Typography>
                    </Box>
                    <Typography variant="h4" color="info.main">
                      {formatCurrency(product.priceCDF, 'CDF')}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Grid>

          {/* Inventory Information */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Informations d'Inventaire
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Inventory sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="subtitle2">Stock Actuel</Typography>
                    </Box>
                    <Typography variant="h4" color="primary.main">
                      {product.stock}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Warning sx={{ mr: 1, color: 'warning.main' }} />
                      <Typography variant="subtitle2">Stock Minimum</Typography>
                    </Box>
                    <Typography variant="h4" color="warning.main">
                      {product.minStock}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <QrCode sx={{ mr: 1, color: 'text.secondary' }} />
                      <Typography variant="subtitle2">Code-barres</Typography>
                    </Box>
                    <Typography variant="h6" color="text.secondary">
                      {product.barcode || 'Non défini'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Grid>

          {/* Timestamps */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Informations de Suivi
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <CalendarToday sx={{ mr: 1, color: 'text.secondary' }} />
                  <Box>
                    <Typography variant="subtitle2">Date de Création</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {new Date(product.createdAt).toLocaleDateString('fr-FR', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <CalendarToday sx={{ mr: 1, color: 'text.secondary' }} />
                  <Box>
                    <Typography variant="subtitle2">Dernière Modification</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {new Date(product.updatedAt).toLocaleDateString('fr-FR', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="contained">
          Fermer
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductDetailsDialog;
