# Maboutique - Système de Gestion de Boutique

Une application de bureau moderne pour la gestion de boutiques destinée aux petites et moyennes entreprises. Cette application prototype sert d'outil de démonstration pour les présentations aux clients et investisseurs potentiels.

## 🚀 Fonctionnalités

### Gestion des Produits et Inventaire
- ✅ Ajouter, modifier, supprimer et consulter les produits
- ✅ Suivi des niveaux de stock en temps réel et alertes de stock faible
- ✅ Support des prix en USD et CDF (Franc Congolais)
- ✅ Gestion des catégories de produits
- ✅ Génération automatique de SKU et codes-barres
- ✅ Interface de simulation de scan de codes-barres

### Gestion des Données CSV
- ✅ Importation de données produits depuis fichiers CSV avec validation
- ✅ Exportation des données d'inventaire et de ventes au format CSV
- ✅ Téléchargement de modèles CSV pour un formatage correct

### Tableau de Bord des Ventes
- ✅ Tableau de bord interactif avec métriques clés
- ✅ Graphiques et diagrammes visuels pour les tendances de ventes
- ✅ Interface de saisie rapide des ventes
- ✅ Mises à jour en temps réel de l'inventaire lors des ventes

### Rapports de Ventes Quotidiens
- ✅ Résumés détaillés des ventes quotidiennes
- ✅ Calculs de revenus et marges bénéficiaires
- ✅ Filtrage par plage de dates et capacités de recherche
- ✅ Génération de rapports imprimables

### Analyses et Insights
- ✅ Identification des produits les plus vendus avec classements visuels
- ✅ Surveillance des niveaux de stock avec suggestions de réapprovisionnement
- ✅ Tendances de performance des ventes dans le temps
- ✅ Analyse du taux de rotation des stocks

### Gestion des Rôles Utilisateurs
- ✅ Système de connexion avec accès basé sur les rôles
- ✅ Trois niveaux d'utilisateurs : Employé/Admin/Superadmin
- ✅ Fonctionnalités exclusives au Superadmin : suppression de produits, modification des enregistrements financiers
- ✅ Matrice de permissions claire définissant les niveaux d'accès

## 🛠️ Technologies Utilisées

- **Frontend**: React 18 avec TypeScript
- **Framework Desktop**: Electron 28
- **UI Library**: Material-UI (MUI) v5
- **Stockage de Données**: LocalStorage (simulation de base de données)
- **Graphiques**: Chart.js avec React-ChartJS-2
- **Outil de Build**: Vite
- **Langue**: Interface entièrement en français

## 📋 Prérequis

- Node.js 16+ 
- npm ou yarn
- Windows, macOS, ou Linux

## 🚀 Installation et Démarrage

### 1. Cloner le projet
```bash
git clone <repository-url>
cd Maboutique
```

### 2. Installer les dépendances
```bash
npm install
```

### 3. Démarrer en mode développement

#### Option A: Application Web uniquement
```bash
npm run dev
```
Puis ouvrir http://localhost:5173 dans votre navigateur.

#### Option B: Application Electron (Recommandé)
```bash
npm run electron-dev
```
Cela lancera automatiquement l'application de bureau Electron.

### 4. Construire pour la production
```bash
# Construire l'application web
npm run build

# Construire l'application Electron
npm run build-electron

# Créer un installateur
npm run dist
```

## 👥 Comptes de Démonstration

L'application inclut des comptes de démonstration pré-configurés :

### Super Administrateur
- **Utilisateur**: `superadmin`
- **Mot de passe**: `super123`
- **Permissions**: Accès complet incluant suppression de produits et modification des enregistrements financiers

### Administrateur
- **Utilisateur**: `admin`
- **Mot de passe**: `admin123`
- **Permissions**: Gestion des produits, ventes, rapports et utilisateurs

### Employé
- **Utilisateur**: `employe`
- **Mot de passe**: `emp123`
- **Permissions**: Consultation des produits, gestion des ventes et consultation des rapports

## 📊 Données de Démonstration

L'application est pré-remplie avec :
- 10 produits d'exemple dans diverses catégories
- 150 ventes simulées sur les 30 derniers jours
- 6 catégories de produits
- Historique des transactions pour les démonstrations

## 🎯 Utilisation

### Navigation
- Utilisez la barre latérale pour naviguer entre les sections
- Le tableau de bord affiche un aperçu des métriques clés
- Les alertes de stock faible apparaissent automatiquement

### Gestion des Produits
- Cliquez sur "Nouveau Produit" pour ajouter des articles
- Utilisez les filtres pour rechercher des produits spécifiques
- Les prix sont automatiquement convertis USD ↔ CDF

### Enregistrement des Ventes
- Sélectionnez un produit dans la liste déroulante
- Entrez la quantité et les informations client
- Le stock est automatiquement mis à jour

### Rapports et Analyses
- Consultez les rapports quotidiens avec filtrage par date
- Analysez les tendances de performance dans la section Analytics
- Exportez les données au format CSV pour analyse externe

## 🔧 Configuration

### Taux de Change
Le taux USD vers CDF est configuré dans `src/components/Products/ProductDialog.jsx` :
```javascript
const cdfPrice = Math.round(usdPrice * 2500); // 1 USD = 2500 CDF
```

### Seuils de Stock
Les alertes de stock faible sont configurables par produit via le champ "Stock Minimum".

## 📦 Structure du Projet

```
Maboutique/
├── electron/           # Configuration Electron
├── src/
│   ├── components/     # Composants React
│   ├── contexts/       # Contextes React (Auth, Data)
│   ├── data/          # Gestion des données et données simulées
│   └── utils/         # Fonctions utilitaires
├── public/            # Assets statiques
└── build/             # Fichiers de build
```

## 🚀 Déploiement

### Application Web
```bash
npm run build
# Déployer le dossier 'build' sur votre serveur web
```

### Application Desktop
```bash
npm run dist
# Les installateurs seront créés dans le dossier 'dist'
```

## 🔒 Sécurité

⚠️ **Note Importante**: Cette application est un prototype de démonstration. Pour un usage en production :
- Implémentez une authentification sécurisée
- Utilisez une vraie base de données
- Ajoutez le chiffrement des données sensibles
- Configurez des sauvegardes automatiques

## 🤝 Support

Pour toute question ou problème :
1. Vérifiez que toutes les dépendances sont installées
2. Assurez-vous que Node.js 16+ est installé
3. Consultez les logs de la console pour les erreurs

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.

---

**Maboutique** - Système de Gestion de Boutique Moderne
Version 1.0.0 - Prototype de Démonstration
