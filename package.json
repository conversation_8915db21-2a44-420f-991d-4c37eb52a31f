{"name": "maboutique-desktop", "version": "1.0.0", "description": "Système de gestion de boutique pour PME", "main": "electron/main.js", "homepage": "./", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "dist-all": "npm run build && electron-builder --win --mac --linux", "pack": "npm run build && electron-builder --dir", "clean": "<PERSON><PERSON>f build dist", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["shop", "management", "inventory", "sales", "desktop", "electron"], "author": "Maboutique Team", "license": "MIT", "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "electron": "^28.1.0", "electron-builder": "^24.9.1", "typescript": "^5.2.2", "vite": "^5.0.8", "wait-on": "^7.2.0"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.2", "@mui/material": "^5.15.2", "@mui/x-charts": "^6.18.1", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "better-sqlite3": "^9.2.2", "chart.js": "^4.4.1", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^3.0.6", "papaparse": "^5.4.1", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "recharts": "^2.8.0"}, "build": {"appId": "com.maboutique.desktop", "productName": "Maboutique", "directories": {"output": "dist"}, "files": ["build/**/*", "electron/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.business"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}