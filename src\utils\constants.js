// Application constants

export const APP_CONFIG = {
  name: 'Maboutique',
  version: '1.0.0',
  description: 'Système de Gestion de Boutique',
  author: 'Maboutique Team',
  website: 'https://maboutique.com'
};

export const CURRENCIES = {
  USD: {
    code: 'USD',
    symbol: '$',
    name: 'Dollar Américain',
    locale: 'en-US'
  },
  CDF: {
    code: 'CDF',
    symbol: 'FC',
    name: 'Franc <PERSON>lais',
    locale: 'fr-CD'
  }
};

export const EXCHANGE_RATE = {
  USD_TO_CDF: 2500, // 1 USD = 2500 CDF (example rate)
  CDF_TO_USD: 0.0004 // 1 CDF = 0.0004 USD
};

export const CATEGORIES = [
  { id: 1, name: 'Électronique', description: 'Appareils électroniques et accessoires', color: 'primary' },
  { id: 2, name: 'Vêtements', description: 'Vêtements pour hommes, femmes et enfants', color: 'secondary' },
  { id: 3, name: 'Alimentation', description: 'Produits alimentaires et boissons', color: 'success' },
  { id: 4, name: 'Cosmétiques', description: 'Produits de beauté et soins personnels', color: 'warning' },
  { id: 5, name: 'Maison', description: 'Articles pour la maison et décoration', color: 'info' },
  { id: 6, name: 'Sport', description: 'Équipements et vêtements de sport', color: 'error' }
];

export const PAYMENT_METHODS = [
  { id: 'cash', name: 'Espèces', icon: '💵', color: 'success' },
  { id: 'card', name: 'Carte', icon: '💳', color: 'primary' },
  { id: 'mobile', name: 'Mobile Money', icon: '📱', color: 'warning' }
];

export const USER_ROLES = {
  EMPLOYEE: {
    id: 'employee',
    name: 'Employé',
    description: 'Accès de base pour effectuer des ventes et consulter les rapports',
    color: 'info',
    permissions: [
      'view_dashboard',
      'view_products',
      'manage_sales',
      'view_reports'
    ]
  },
  ADMIN: {
    id: 'admin',
    name: 'Administrateur',
    description: 'Gestion complète des produits, ventes et utilisateurs',
    color: 'warning',
    permissions: [
      'view_dashboard',
      'manage_products',
      'manage_sales',
      'view_reports',
      'view_analytics',
      'manage_users'
    ]
  },
  SUPERADMIN: {
    id: 'superadmin',
    name: 'Super Administrateur',
    description: 'Accès complet incluant la suppression et modification des enregistrements',
    color: 'error',
    permissions: [
      'view_dashboard',
      'manage_products',
      'delete_products',
      'manage_sales',
      'view_reports',
      'view_analytics',
      'manage_users',
      'modify_financial_records',
      'system_settings'
    ]
  }
};

export const STOCK_LEVELS = {
  OUT_OF_STOCK: { min: 0, max: 0, label: 'Épuisé', color: 'error', severity: 'high' },
  LOW_STOCK: { min: 1, max: 10, label: 'Stock Faible', color: 'warning', severity: 'medium' },
  MEDIUM_STOCK: { min: 11, max: 50, label: 'Stock Moyen', color: 'info', severity: 'low' },
  HIGH_STOCK: { min: 51, max: Infinity, label: 'Stock Élevé', color: 'success', severity: 'none' }
};

export const DATE_FORMATS = {
  SHORT: 'dd/MM/yyyy',
  LONG: 'dd MMMM yyyy',
  WITH_TIME: 'dd/MM/yyyy HH:mm',
  TIME_ONLY: 'HH:mm'
};

export const CHART_COLORS = {
  PRIMARY: '#1976d2',
  SECONDARY: '#dc004e',
  SUCCESS: '#2e7d32',
  WARNING: '#ed6c02',
  ERROR: '#d32f2f',
  INFO: '#0288d1',
  GREY: '#757575'
};

export const CHART_GRADIENTS = {
  BLUE: ['#1976d2', '#42a5f5'],
  GREEN: ['#2e7d32', '#66bb6a'],
  ORANGE: ['#ed6c02', '#ffb74d'],
  RED: ['#d32f2f', '#ef5350'],
  PURPLE: ['#7b1fa2', '#ba68c8']
};

export const NAVIGATION_ITEMS = [
  {
    id: 'dashboard',
    label: 'Tableau de Bord',
    path: '/dashboard',
    icon: 'Dashboard',
    permission: 'view_dashboard'
  },
  {
    id: 'products',
    label: 'Produits',
    path: '/products',
    icon: 'Inventory',
    permission: 'view_products'
  },
  {
    id: 'sales',
    label: 'Ventes',
    path: '/sales',
    icon: 'PointOfSale',
    permission: 'manage_sales'
  },
  {
    id: 'reports',
    label: 'Rapports',
    path: '/reports',
    icon: 'Assessment',
    permission: 'view_reports'
  },
  {
    id: 'analytics',
    label: 'Analyses',
    path: '/analytics',
    icon: 'Analytics',
    permission: 'view_analytics'
  },
  {
    id: 'users',
    label: 'Utilisateurs',
    path: '/users',
    icon: 'People',
    permission: 'manage_users'
  }
];

export const DEMO_CREDENTIALS = [
  {
    username: 'superadmin',
    password: 'super123',
    role: 'superadmin',
    name: 'Super Administrateur',
    email: '<EMAIL>'
  },
  {
    username: 'admin',
    password: 'admin123',
    role: 'admin',
    name: 'Administrateur Principal',
    email: '<EMAIL>'
  },
  {
    username: 'employe',
    password: 'emp123',
    role: 'employee',
    name: 'Employé Boutique',
    email: '<EMAIL>'
  }
];

export const LOCAL_STORAGE_KEYS = {
  USER: 'maboutique_user',
  PRODUCTS: 'maboutique_products',
  SALES: 'maboutique_sales',
  CATEGORIES: 'maboutique_categories',
  SETTINGS: 'maboutique_settings'
};

export const API_ENDPOINTS = {
  // For future API integration
  BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:3001/api',
  PRODUCTS: '/products',
  SALES: '/sales',
  USERS: '/users',
  AUTH: '/auth',
  REPORTS: '/reports'
};

export const VALIDATION_RULES = {
  PRODUCT: {
    NAME: { required: true, minLength: 2, maxLength: 100 },
    PRICE: { required: true, min: 0.01, max: 999999 },
    SKU: { required: true, minLength: 3, maxLength: 20 },
    STOCK: { required: true, min: 0, max: 999999 },
    CATEGORY: { required: true }
  },
  SALE: {
    PRODUCT: { required: true },
    QUANTITY: { required: true, min: 1, max: 1000 },
    CUSTOMER: { required: true, minLength: 2, maxLength: 100 },
    PAYMENT_METHOD: { required: true }
  },
  USER: {
    NAME: { required: true, minLength: 2, maxLength: 100 },
    EMAIL: { required: true, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
    USERNAME: { required: true, minLength: 3, maxLength: 20 },
    PASSWORD: { required: true, minLength: 6, maxLength: 50 }
  }
};

export const ERROR_MESSAGES = {
  NETWORK: 'Erreur de connexion réseau',
  UNAUTHORIZED: 'Accès non autorisé',
  FORBIDDEN: 'Action non autorisée',
  NOT_FOUND: 'Ressource non trouvée',
  SERVER_ERROR: 'Erreur serveur interne',
  VALIDATION: 'Données invalides',
  UNKNOWN: 'Erreur inconnue'
};

export const SUCCESS_MESSAGES = {
  PRODUCT_CREATED: 'Produit créé avec succès',
  PRODUCT_UPDATED: 'Produit mis à jour avec succès',
  PRODUCT_DELETED: 'Produit supprimé avec succès',
  SALE_CREATED: 'Vente enregistrée avec succès',
  SALE_UPDATED: 'Vente mise à jour avec succès',
  SALE_DELETED: 'Vente supprimée avec succès',
  DATA_EXPORTED: 'Données exportées avec succès',
  DATA_IMPORTED: 'Données importées avec succès'
};
