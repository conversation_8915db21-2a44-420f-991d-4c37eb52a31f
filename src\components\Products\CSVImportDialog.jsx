import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Alert,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  Divider,
  Paper
} from '@mui/material';
import { CloudUpload, GetApp, CheckCircle, Error } from '@mui/icons-material';
import { useData } from '../../contexts/DataContext';
import { parseCSV, validateProductCSV, generateProductTemplate, downloadCSV } from '../../data/csvHandler';

const CSVImportDialog = ({ open, onClose }) => {
  const { addProduct } = useData();
  const [file, setFile] = useState(null);
  const [importing, setImporting] = useState(false);
  const [results, setResults] = useState(null);

  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile && selectedFile.type === 'text/csv') {
      setFile(selectedFile);
      setResults(null);
    } else {
      alert('Veuillez sélectionner un fichier CSV valide.');
    }
  };

  const downloadTemplate = () => {
    const templateContent = generateProductTemplate();
    const blob = new Blob([templateContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'template_produits.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };





  const handleImport = async () => {
    if (!file) return;

    setImporting(true);
    
    try {
      const text = await file.text();
      const data = parseCSV(text);

      const validation = validateProductCSV(data);

      // Import valid products
      validation.valid.forEach(productData => {
        addProduct(productData);
      });

      setResults({
        total: validation.totalRows,
        imported: validation.valid.length,
        errors: validation.errors.length,
        invalidProducts: validation.errors
      });

    } catch (error) {
      console.error('Error importing CSV:', error);
      alert('Erreur lors de l\'importation du fichier CSV.');
    } finally {
      setImporting(false);
    }
  };

  const handleClose = () => {
    setFile(null);
    setResults(null);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        Importer des Produits depuis CSV
      </DialogTitle>

      <DialogContent>
        {!results ? (
          <Box>
            {/* Instructions */}
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body2">
                Importez vos produits en utilisant un fichier CSV. 
                Téléchargez d'abord le modèle pour voir le format requis.
              </Typography>
            </Alert>

            {/* Template Download */}
            <Box sx={{ mb: 3, textAlign: 'center' }}>
              <Button
                variant="outlined"
                startIcon={<GetApp />}
                onClick={downloadTemplate}
              >
                Télécharger le Modèle CSV
              </Button>
            </Box>

            <Divider sx={{ mb: 3 }} />

            {/* File Upload */}
            <Box sx={{ textAlign: 'center' }}>
              <input
                accept=".csv"
                style={{ display: 'none' }}
                id="csv-file-input"
                type="file"
                onChange={handleFileChange}
              />
              <label htmlFor="csv-file-input">
                <Button
                  variant="contained"
                  component="span"
                  startIcon={<CloudUpload />}
                  size="large"
                >
                  Sélectionner un Fichier CSV
                </Button>
              </label>
              
              {file && (
                <Typography variant="body2" sx={{ mt: 2 }}>
                  Fichier sélectionné: {file.name}
                </Typography>
              )}
            </Box>

            {/* Format Requirements */}
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                Format Requis:
              </Typography>
              <Paper sx={{ p: 2, backgroundColor: '#f5f5f5' }}>
                <Typography variant="body2" component="div">
                  <strong>Colonnes requises:</strong>
                  <ul>
                    <li>Nom - Nom du produit</li>
                    <li>Description - Description du produit (optionnel)</li>
                    <li>Prix USD - Prix en dollars américains</li>
                    <li>Prix CDF - Prix en francs congolais</li>
                    <li>SKU - Code produit unique</li>
                    <li>Catégorie ID - ID de catégorie (1-6)</li>
                    <li>Stock - Quantité en stock</li>
                    <li>Stock Min - Stock minimum</li>
                    <li>Code-barres - Code-barres (optionnel)</li>
                  </ul>
                </Typography>
              </Paper>
            </Box>

            {importing && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="body2" gutterBottom>
                  Importation en cours...
                </Typography>
                <LinearProgress />
              </Box>
            )}
          </Box>
        ) : (
          <Box>
            {/* Import Results */}
            <Alert 
              severity={results.errors === 0 ? "success" : "warning"} 
              sx={{ mb: 3 }}
            >
              <Typography variant="body1">
                <strong>Importation terminée:</strong><br />
                {results.imported} produit(s) importé(s) avec succès sur {results.total} total.
                {results.errors > 0 && ` ${results.errors} erreur(s) détectée(s).`}
              </Typography>
            </Alert>

            {results.invalidProducts.length > 0 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Produits avec erreurs:
                </Typography>
                <List>
                  {results.invalidProducts.map((item, index) => (
                    <ListItem key={index} divider>
                      <ListItemText
                        primary={`Ligne ${item.row}`}
                        secondary={
                          <Box>
                            <Typography variant="body2" color="error">
                              Erreurs: {item.errors.join(', ')}
                            </Typography>
                          </Box>
                        }
                      />
                      <Error color="error" />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>
          {results ? 'Fermer' : 'Annuler'}
        </Button>
        {!results && (
          <Button
            onClick={handleImport}
            variant="contained"
            disabled={!file || importing}
          >
            {importing ? 'Importation...' : 'Importer'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default CSVImportDialog;
