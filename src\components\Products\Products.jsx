import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  TextField,
  InputAdornment,
  Fab,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Alert
} from '@mui/material';
import {
  Add,
  Search,
  FilterList,
  MoreVert,
  Edit,
  Delete,
  Visibility,
  Warning,
  GetApp,
  Publish
} from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';
import { useData } from '../../contexts/DataContext';
import { useAuth } from '../../contexts/AuthContext';
import { exportProductsData } from '../../data/csvHandler';
import ProductDialog from './ProductDialog';
import ProductDetailsDialog from './ProductDetailsDialog';
import CSVImportDialog from './CSVImportDialog';

const Products = () => {
  const { products, deleteProduct } = useData();
  const { hasPermission } = useAuth();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [productDialog, setProductDialog] = useState({ open: false, product: null });
  const [detailsDialog, setDetailsDialog] = useState({ open: false, product: null });
  const [csvDialog, setCsvDialog] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedProduct, setSelectedProduct] = useState(null);

  // Filter products
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !selectedCategory || product.categoryId.toString() === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // DataGrid columns
  const columns = [
    {
      field: 'name',
      headerName: 'Nom du Produit',
      flex: 1,
      minWidth: 200,
      renderCell: (params) => (
        <Box>
          <Typography variant="body2" fontWeight="medium">
            {params.value}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            SKU: {params.row.sku}
          </Typography>
        </Box>
      )
    },
    {
      field: 'price',
      headerName: 'Prix (USD)',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2">
          ${params.value}
        </Typography>
      )
    },
    {
      field: 'priceCDF',
      headerName: 'Prix (CDF)',
      width: 130,
      renderCell: (params) => (
        <Typography variant="body2">
          {params.value?.toLocaleString()} FC
        </Typography>
      )
    },
    {
      field: 'stock',
      headerName: 'Stock',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={params.value <= params.row.minStock ? 'error' : 
                 params.value <= params.row.minStock * 2 ? 'warning' : 'success'}
          size="small"
        />
      )
    },
    {
      field: 'category',
      headerName: 'Catégorie',
      width: 150,
      renderCell: (params) => (
        <Chip
          label={getCategoryName(params.row.categoryId)}
          variant="outlined"
          size="small"
        />
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      sortable: false,
      renderCell: (params) => (
        <IconButton
          size="small"
          onClick={(e) => handleMenuOpen(e, params.row)}
        >
          <MoreVert />
        </IconButton>
      )
    }
  ];

  const getCategoryName = (categoryId) => {
    const categories = {
      1: 'Électronique',
      2: 'Vêtements',
      3: 'Alimentation',
      4: 'Cosmétiques',
      5: 'Maison',
      6: 'Sport'
    };
    return categories[categoryId] || 'Autre';
  };

  const handleMenuOpen = (event, product) => {
    setAnchorEl(event.currentTarget);
    setSelectedProduct(product);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedProduct(null);
  };

  const handleAddProduct = () => {
    setProductDialog({ open: true, product: null });
  };

  const handleEditProduct = () => {
    setProductDialog({ open: true, product: selectedProduct });
    handleMenuClose();
  };

  const handleViewProduct = () => {
    setDetailsDialog({ open: true, product: selectedProduct });
    handleMenuClose();
  };

  const handleDeleteProduct = () => {
    if (selectedProduct && hasPermission('delete_products')) {
      if (window.confirm(`Êtes-vous sûr de vouloir supprimer "${selectedProduct.name}" ?`)) {
        deleteProduct(selectedProduct.id);
      }
    }
    handleMenuClose();
  };

  const handleExportCSV = () => {
    const csvContent = exportProductsData(filteredProducts);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `produits_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const lowStockCount = products.filter(p => p.stock <= p.minStock).length;

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Gestion des Produits
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            {products.length} produits • {lowStockCount} en stock faible
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<GetApp />}
            onClick={handleExportCSV}
          >
            Exporter CSV
          </Button>
          <Button
            variant="outlined"
            startIcon={<Publish />}
            onClick={() => setCsvDialog(true)}
          >
            Importer CSV
          </Button>
          {hasPermission('manage_products') && (
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={handleAddProduct}
            >
              Nouveau Produit
            </Button>
          )}
        </Box>
      </Box>

      {/* Low stock alert */}
      {lowStockCount > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <strong>{lowStockCount} produit(s)</strong> ont un stock faible ou épuisé.
        </Alert>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Rechercher par nom ou SKU..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                select
                label="Catégorie"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                SelectProps={{ native: true }}
              >
                <option value="">Toutes les catégories</option>
                <option value="1">Électronique</option>
                <option value="2">Vêtements</option>
                <option value="3">Alimentation</option>
                <option value="4">Cosmétiques</option>
                <option value="5">Maison</option>
                <option value="6">Sport</option>
              </TextField>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FilterList />}
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('');
                }}
              >
                Réinitialiser
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card>
        <Box sx={{ height: 600, width: '100%' }}>
          <DataGrid
            rows={filteredProducts}
            columns={columns}
            pageSize={10}
            rowsPerPageOptions={[10, 25, 50]}
            disableSelectionOnClick
            localeText={{
              noRowsLabel: 'Aucun produit trouvé',
              footerRowSelected: (count) => `${count} ligne(s) sélectionnée(s)`,
            }}
          />
        </Box>
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleViewProduct}>
          <Visibility sx={{ mr: 1 }} fontSize="small" />
          Voir Détails
        </MenuItem>
        {hasPermission('manage_products') && (
          <MenuItem onClick={handleEditProduct}>
            <Edit sx={{ mr: 1 }} fontSize="small" />
            Modifier
          </MenuItem>
        )}
        {hasPermission('delete_products') && (
          <MenuItem onClick={handleDeleteProduct} sx={{ color: 'error.main' }}>
            <Delete sx={{ mr: 1 }} fontSize="small" />
            Supprimer
          </MenuItem>
        )}
      </Menu>

      {/* Floating Action Button for Mobile */}
      {hasPermission('manage_products') && (
        <Fab
          color="primary"
          aria-label="add"
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            display: { xs: 'flex', md: 'none' }
          }}
          onClick={handleAddProduct}
        >
          <Add />
        </Fab>
      )}

      {/* Dialogs */}
      <ProductDialog
        open={productDialog.open}
        product={productDialog.product}
        onClose={() => setProductDialog({ open: false, product: null })}
      />

      <ProductDetailsDialog
        open={detailsDialog.open}
        product={detailsDialog.product}
        onClose={() => setDetailsDialog({ open: false, product: null })}
      />

      <CSVImportDialog
        open={csvDialog}
        onClose={() => setCsvDialog(false)}
      />
    </Box>
  );
};

export default Products;
