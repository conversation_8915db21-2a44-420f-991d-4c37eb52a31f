import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>graphy,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Divider
} from '@mui/material';
import {
  Print,
  GetApp,
  CalendarToday,
  AttachMoney,
  TrendingUp,
  Assessment
} from '@mui/icons-material';
import { useData } from '../../contexts/DataContext';

const Reports = () => {
  const { sales, products, getTotalRevenue } = useData();
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  // Filter sales by date range
  const filteredSales = sales.filter(sale => {
    const saleDate = new Date(sale.date);
    const start = startDate ? new Date(startDate) : null;
    const end = endDate ? new Date(endDate + 'T23:59:59') : null;
    
    return (!start || saleDate >= start) && (!end || saleDate <= end);
  });

  // Calculate metrics
  const totalRevenue = filteredSales.reduce((sum, sale) => sum + sale.totalPrice, 0);
  const totalSales = filteredSales.length;
  const averageSale = totalSales > 0 ? totalRevenue / totalSales : 0;

  // Group sales by date
  const salesByDate = filteredSales.reduce((acc, sale) => {
    const date = sale.date.split('T')[0];
    if (!acc[date]) {
      acc[date] = {
        date,
        sales: [],
        totalRevenue: 0,
        totalQuantity: 0
      };
    }
    acc[date].sales.push(sale);
    acc[date].totalRevenue += sale.totalPrice;
    acc[date].totalQuantity += sale.quantity;
    return acc;
  }, {});

  const dailyReports = Object.values(salesByDate).sort((a, b) => new Date(b.date) - new Date(a.date));

  // Product performance
  const productPerformance = products.map(product => {
    const productSales = filteredSales.filter(sale => sale.productId === product.id);
    const totalSold = productSales.reduce((sum, sale) => sum + sale.quantity, 0);
    const revenue = productSales.reduce((sum, sale) => sum + sale.totalPrice, 0);
    
    return {
      ...product,
      totalSold,
      revenue,
      salesCount: productSales.length
    };
  }).sort((a, b) => b.revenue - a.revenue);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExportCSV = () => {
    const csvData = [
      ['Date', 'Produit', 'Client', 'Quantité', 'Prix Unitaire', 'Total', 'Paiement'],
      ...filteredSales.map(sale => [
        new Date(sale.date).toLocaleDateString('fr-FR'),
        sale.productName,
        sale.customerName,
        sale.quantity,
        sale.unitPrice,
        sale.totalPrice,
        sale.paymentMethod
      ])
    ];

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `rapport_ventes_${startDate || 'debut'}_${endDate || 'fin'}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Rapports de Ventes
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Analyse détaillée des performances de vente
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<Print />}
            onClick={handlePrint}
          >
            Imprimer
          </Button>
          <Button
            variant="outlined"
            startIcon={<GetApp />}
            onClick={handleExportCSV}
          >
            Exporter CSV
          </Button>
        </Box>
      </Box>

      {/* Date Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Filtres de Date
          </Typography>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="date"
                label="Date de début"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="date"
                label="Date de fin"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => {
                  setStartDate('');
                  setEndDate('');
                }}
              >
                Réinitialiser
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Revenus Total
                  </Typography>
                  <Typography variant="h5" component="div">
                    {formatCurrency(totalRevenue)}
                  </Typography>
                </Box>
                <AttachMoney sx={{ fontSize: 40, color: 'success.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Nombre de Ventes
                  </Typography>
                  <Typography variant="h5" component="div">
                    {totalSales}
                  </Typography>
                </Box>
                <Assessment sx={{ fontSize: 40, color: 'primary.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Vente Moyenne
                  </Typography>
                  <Typography variant="h5" component="div">
                    {formatCurrency(averageSale)}
                  </Typography>
                </Box>
                <TrendingUp sx={{ fontSize: 40, color: 'info.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Jours Analysés
                  </Typography>
                  <Typography variant="h5" component="div">
                    {dailyReports.length}
                  </Typography>
                </Box>
                <CalendarToday sx={{ fontSize: 40, color: 'warning.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Daily Sales Report */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Rapport Quotidien des Ventes
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Date</TableCell>
                  <TableCell align="right">Nombre de Ventes</TableCell>
                  <TableCell align="right">Quantité Totale</TableCell>
                  <TableCell align="right">Revenus</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {dailyReports.map((day) => (
                  <TableRow key={day.date}>
                    <TableCell>
                      {new Date(day.date).toLocaleDateString('fr-FR', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </TableCell>
                    <TableCell align="right">
                      <Chip label={day.sales.length} color="primary" size="small" />
                    </TableCell>
                    <TableCell align="right">{day.totalQuantity}</TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight="bold" color="success.main">
                        {formatCurrency(day.totalRevenue)}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
                {dailyReports.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      <Typography variant="body2" color="text.secondary">
                        Aucune vente trouvée pour la période sélectionnée
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Product Performance */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Performance des Produits
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Produit</TableCell>
                  <TableCell align="right">Quantité Vendue</TableCell>
                  <TableCell align="right">Nombre de Ventes</TableCell>
                  <TableCell align="right">Revenus Générés</TableCell>
                  <TableCell align="right">Stock Restant</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {productPerformance.slice(0, 10).map((product) => (
                  <TableRow key={product.id}>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {product.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          SKU: {product.sku}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Chip 
                        label={product.totalSold} 
                        color={product.totalSold > 0 ? "success" : "default"} 
                        size="small" 
                      />
                    </TableCell>
                    <TableCell align="right">{product.salesCount}</TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight="bold" color="success.main">
                        {formatCurrency(product.revenue)}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Chip 
                        label={product.stock} 
                        color={product.stock <= product.minStock ? "error" : "default"} 
                        size="small" 
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Reports;
