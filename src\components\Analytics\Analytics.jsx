import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Star,
  Warning,
  Inventory,
  AttachMoney
} from '@mui/icons-material';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js';
import { useData } from '../../contexts/DataContext';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const Analytics = () => {
  const { 
    sales, 
    products, 
    getTopProducts, 
    getLowStockProducts,
    getDailySales 
  } = useData();
  
  const [timeRange, setTimeRange] = useState('30');

  // Get data based on time range
  const dailySalesData = getDailySales(parseInt(timeRange));
  const topProducts = getTopProducts(10);
  const lowStockProducts = getLowStockProducts();

  // Sales trend chart
  const salesTrendData = {
    labels: dailySalesData.map(item => 
      new Date(item.date).toLocaleDateString('fr-FR', { 
        month: 'short', 
        day: 'numeric' 
      })
    ),
    datasets: [
      {
        label: 'Revenus Quotidiens (USD)',
        data: dailySalesData.map(item => item.revenue),
        borderColor: 'rgb(25, 118, 210)',
        backgroundColor: 'rgba(25, 118, 210, 0.1)',
        fill: true,
        tension: 0.4,
      },
    ],
  };

  // Top products chart
  const topProductsData = {
    labels: topProducts.slice(0, 5).map(item => item.product.name),
    datasets: [
      {
        label: 'Quantité Vendue',
        data: topProducts.slice(0, 5).map(item => item.quantity),
        backgroundColor: [
          'rgba(255, 99, 132, 0.8)',
          'rgba(54, 162, 235, 0.8)',
          'rgba(255, 205, 86, 0.8)',
          'rgba(75, 192, 192, 0.8)',
          'rgba(153, 102, 255, 0.8)',
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 205, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Payment methods distribution
  const paymentMethodsData = sales.reduce((acc, sale) => {
    acc[sale.paymentMethod] = (acc[sale.paymentMethod] || 0) + 1;
    return acc;
  }, {});

  const paymentMethodsChartData = {
    labels: Object.keys(paymentMethodsData),
    datasets: [
      {
        data: Object.values(paymentMethodsData),
        backgroundColor: [
          'rgba(76, 175, 80, 0.8)',
          'rgba(33, 150, 243, 0.8)',
          'rgba(255, 152, 0, 0.8)',
        ],
        borderColor: [
          'rgba(76, 175, 80, 1)',
          'rgba(33, 150, 243, 1)',
          'rgba(255, 152, 0, 1)',
        ],
        borderWidth: 2,
      },
    ],
  };

  // Category performance
  const categoryPerformance = products.reduce((acc, product) => {
    const categoryName = getCategoryName(product.categoryId);
    const productSales = sales.filter(sale => sale.productId === product.id);
    const revenue = productSales.reduce((sum, sale) => sum + sale.totalPrice, 0);
    
    if (!acc[categoryName]) {
      acc[categoryName] = { revenue: 0, products: 0, sales: 0 };
    }
    
    acc[categoryName].revenue += revenue;
    acc[categoryName].products += 1;
    acc[categoryName].sales += productSales.length;
    
    return acc;
  }, {});

  const getCategoryName = (categoryId) => {
    const categories = {
      1: 'Électronique',
      2: 'Vêtements',
      3: 'Alimentation',
      4: 'Cosmétiques',
      5: 'Maison',
      6: 'Sport'
    };
    return categories[categoryId] || 'Autre';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
    },
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Analyses et Insights
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Analyse approfondie des performances de votre boutique
          </Typography>
        </Box>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Période d'analyse</InputLabel>
          <Select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            label="Période d'analyse"
          >
            <MenuItem value="7">7 derniers jours</MenuItem>
            <MenuItem value="14">14 derniers jours</MenuItem>
            <MenuItem value="30">30 derniers jours</MenuItem>
            <MenuItem value="60">60 derniers jours</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Produits Actifs
                  </Typography>
                  <Typography variant="h5" component="div">
                    {products.filter(p => p.stock > 0).length}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    sur {products.length} total
                  </Typography>
                </Box>
                <Inventory sx={{ fontSize: 40, color: 'primary.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Alertes Stock
                  </Typography>
                  <Typography variant="h5" component="div" color="warning.main">
                    {lowStockProducts.length}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    produits en stock faible
                  </Typography>
                </Box>
                <Warning sx={{ fontSize: 40, color: 'warning.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Meilleur Produit
                  </Typography>
                  <Typography variant="h6" component="div">
                    {topProducts[0]?.product.name || 'Aucun'}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {topProducts[0]?.quantity || 0} vendus
                  </Typography>
                </Box>
                <Star sx={{ fontSize: 40, color: 'success.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Valeur Stock
                  </Typography>
                  <Typography variant="h6" component="div">
                    {formatCurrency(
                      products.reduce((sum, p) => sum + (p.price * p.stock), 0)
                    )}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    valeur totale
                  </Typography>
                </Box>
                <AttachMoney sx={{ fontSize: 40, color: 'info.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Sales Trend */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Tendance des Ventes ({timeRange} derniers jours)
              </Typography>
              <Box sx={{ height: 300 }}>
                <Line data={salesTrendData} options={chartOptions} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Payment Methods */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Modes de Paiement
              </Typography>
              <Box sx={{ height: 300 }}>
                <Doughnut data={paymentMethodsChartData} options={chartOptions} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Top Products */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Top 5 Produits les Plus Vendus
              </Typography>
              <Box sx={{ height: 300 }}>
                <Bar data={topProductsData} options={chartOptions} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Category Performance */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Performance par Catégorie
              </Typography>
              <List>
                {Object.entries(categoryPerformance)
                  .sort(([,a], [,b]) => b.revenue - a.revenue)
                  .map(([category, data]) => (
                    <ListItem key={category}>
                      <ListItemIcon>
                        <TrendingUp color={data.revenue > 0 ? 'success' : 'disabled'} />
                      </ListItemIcon>
                      <ListItemText
                        primary={category}
                        secondary={
                          <Box>
                            <Typography variant="body2">
                              Revenus: {formatCurrency(data.revenue)}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {data.products} produits • {data.sales} ventes
                            </Typography>
                          </Box>
                        }
                      />
                      <Box sx={{ minWidth: 100 }}>
                        <LinearProgress
                          variant="determinate"
                          value={Math.min((data.revenue / Math.max(...Object.values(categoryPerformance).map(c => c.revenue))) * 100, 100)}
                          sx={{ mb: 1 }}
                        />
                      </Box>
                    </ListItem>
                  ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Insights and Recommendations */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Produits les Plus Performants
              </Typography>
              <List>
                {topProducts.slice(0, 5).map((item, index) => (
                  <ListItem key={item.product.id}>
                    <ListItemIcon>
                      <Chip 
                        label={index + 1} 
                        color="primary" 
                        size="small" 
                        sx={{ minWidth: 24, height: 24 }}
                      />
                    </ListItemIcon>
                    <ListItemText
                      primary={item.product.name}
                      secondary={`${item.quantity} vendus • ${formatCurrency(item.product.price * item.quantity)}`}
                    />
                    <TrendingUp color="success" />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Alertes de Stock
              </Typography>
              <List>
                {lowStockProducts.slice(0, 5).map((product) => (
                  <ListItem key={product.id}>
                    <ListItemIcon>
                      <Warning color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary={product.name}
                      secondary={`Stock: ${product.stock} (Min: ${product.minStock})`}
                    />
                    <Chip 
                      label="Stock Faible" 
                      color="warning" 
                      size="small" 
                    />
                  </ListItem>
                ))}
                {lowStockProducts.length === 0 && (
                  <ListItem>
                    <ListItemText
                      primary="Aucune alerte de stock"
                      secondary="Tous les produits ont un stock suffisant"
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Analytics;
