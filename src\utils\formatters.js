// Utility functions for formatting data

export const formatCurrency = (amount, currency = 'USD') => {
  if (currency === 'CDF') {
    return new Intl.NumberFormat('fr-CD', {
      style: 'currency',
      currency: 'CDF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

export const formatDate = (date, locale = 'fr-FR', options = {}) => {
  const defaultOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    ...options
  };
  
  return new Date(date).toLocaleDateString(locale, defaultOptions);
};

export const formatDateTime = (date, locale = 'fr-FR') => {
  return new Date(date).toLocaleString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export const formatTime = (date, locale = 'fr-FR') => {
  return new Date(date).toLocaleTimeString(locale, {
    hour: '2-digit',
    minute: '2-digit'
  });
};

export const formatNumber = (number, locale = 'fr-FR') => {
  return new Intl.NumberFormat(locale).format(number);
};

export const formatPercentage = (value, total, decimals = 1) => {
  if (total === 0) return '0%';
  const percentage = (value / total) * 100;
  return `${percentage.toFixed(decimals)}%`;
};

export const getCategoryName = (categoryId) => {
  const categories = {
    1: 'Électronique',
    2: 'Vêtements',
    3: 'Alimentation',
    4: 'Cosmétiques',
    5: 'Maison',
    6: 'Sport'
  };
  return categories[categoryId] || 'Autre';
};

export const getCategoryColor = (categoryId) => {
  const colors = {
    1: 'primary',
    2: 'secondary',
    3: 'success',
    4: 'warning',
    5: 'info',
    6: 'error'
  };
  return colors[categoryId] || 'default';
};

export const getRoleLabel = (role) => {
  const roles = {
    'superadmin': 'Super Administrateur',
    'admin': 'Administrateur',
    'employee': 'Employé'
  };
  return roles[role] || role;
};

export const getRoleColor = (role) => {
  const colors = {
    'superadmin': 'error',
    'admin': 'warning',
    'employee': 'info'
  };
  return colors[role] || 'default';
};

export const getPaymentMethodColor = (method) => {
  const colors = {
    'Espèces': 'success',
    'Carte': 'primary',
    'Mobile Money': 'warning'
  };
  return colors[method] || 'default';
};

export const getStockStatus = (stock, minStock) => {
  if (stock === 0) {
    return { label: 'Épuisé', color: 'error', severity: 'high' };
  } else if (stock <= minStock) {
    return { label: 'Stock Faible', color: 'warning', severity: 'medium' };
  } else if (stock <= minStock * 2) {
    return { label: 'Stock Moyen', color: 'info', severity: 'low' };
  } else {
    return { label: 'En Stock', color: 'success', severity: 'none' };
  }
};

export const generateSKU = (categoryId, productName) => {
  const categoryCode = getCategoryName(categoryId).substring(0, 3).toUpperCase();
  const nameCode = productName.substring(0, 3).toUpperCase().replace(/[^A-Z]/g, '');
  const randomCode = Math.random().toString(36).substring(2, 6).toUpperCase();
  return `${categoryCode}-${nameCode}-${randomCode}`;
};

export const generateBarcode = () => {
  // Generate a 13-digit EAN barcode
  const countryCode = '123'; // Example country code
  const manufacturerCode = Math.floor(Math.random() * 100000).toString().padStart(5, '0');
  const productCode = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  
  // Calculate check digit (simplified)
  const digits = (countryCode + manufacturerCode + productCode).split('').map(Number);
  let sum = 0;
  for (let i = 0; i < digits.length; i++) {
    sum += digits[i] * (i % 2 === 0 ? 1 : 3);
  }
  const checkDigit = (10 - (sum % 10)) % 10;
  
  return countryCode + manufacturerCode + productCode + checkDigit;
};

export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhone = (phone) => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
};

export const truncateText = (text, maxLength = 50) => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

export const capitalizeFirst = (str) => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

export const getInitials = (name) => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

export const calculateTotalValue = (products) => {
  return products.reduce((total, product) => {
    return total + (product.price * product.stock);
  }, 0);
};

export const calculateAveragePrice = (products) => {
  if (products.length === 0) return 0;
  const total = products.reduce((sum, product) => sum + product.price, 0);
  return total / products.length;
};

export const getDateRange = (days) => {
  const end = new Date();
  const start = new Date();
  start.setDate(start.getDate() - days);
  return { start, end };
};

export const isToday = (date) => {
  const today = new Date();
  const checkDate = new Date(date);
  return checkDate.toDateString() === today.toDateString();
};

export const isThisWeek = (date) => {
  const today = new Date();
  const checkDate = new Date(date);
  const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
  return checkDate >= startOfWeek;
};

export const isThisMonth = (date) => {
  const today = new Date();
  const checkDate = new Date(date);
  return checkDate.getMonth() === today.getMonth() && 
         checkDate.getFullYear() === today.getFullYear();
};
