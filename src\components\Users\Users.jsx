import React, { useState } from 'react';
import {
  <PERSON>,
  Ty<PERSON><PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Avatar,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert
} from '@mui/material';
import {
  Add,
  MoreVert,
  Edit,
  Delete,
  Person,
  AdminPanelSettings,
  SupervisorAccount,
  Work
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const Users = () => {
  const { user, mockUsers, hasPermission } = useAuth();
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);

  const handleMenuOpen = (event, userData) => {
    setAnchorEl(event.currentTarget);
    setSelectedUser(userData);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedUser(null);
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'superadmin':
        return <SupervisorAccount />;
      case 'admin':
        return <AdminPanelSettings />;
      case 'employee':
        return <Work />;
      default:
        return <Person />;
    }
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'superadmin':
        return 'error';
      case 'admin':
        return 'warning';
      case 'employee':
        return 'info';
      default:
        return 'default';
    }
  };

  const getRoleLabel = (role) => {
    switch (role) {
      case 'superadmin':
        return 'Super Administrateur';
      case 'admin':
        return 'Administrateur';
      case 'employee':
        return 'Employé';
      default:
        return role;
    }
  };

  const getInitials = (name) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const permissions = {
    superadmin: [
      'Voir le tableau de bord',
      'Gérer les produits',
      'Supprimer les produits',
      'Gérer les ventes',
      'Voir les rapports',
      'Voir les analyses',
      'Gérer les utilisateurs',
      'Modifier les enregistrements financiers',
      'Paramètres système'
    ],
    admin: [
      'Voir le tableau de bord',
      'Gérer les produits',
      'Gérer les ventes',
      'Voir les rapports',
      'Voir les analyses',
      'Gérer les utilisateurs'
    ],
    employee: [
      'Voir le tableau de bord',
      'Voir les produits',
      'Gérer les ventes',
      'Voir les rapports'
    ]
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Gestion des Utilisateurs
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Gérez les comptes utilisateurs et leurs permissions
          </Typography>
        </Box>
        {hasPermission('manage_users') && (
          <Button
            variant="contained"
            startIcon={<Add />}
            disabled
          >
            Nouvel Utilisateur
          </Button>
        )}
      </Box>

      {/* Demo Notice */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <strong>Mode Démonstration:</strong> Cette section affiche les utilisateurs de démonstration. 
        Dans une version de production, vous pourriez créer, modifier et supprimer des comptes utilisateurs.
      </Alert>

      {/* User Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {mockUsers.map((userData) => (
          <Grid item xs={12} md={6} lg={4} key={userData.id}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar 
                      sx={{ 
                        width: 56, 
                        height: 56, 
                        bgcolor: getRoleColor(userData.role) + '.main',
                        mr: 2
                      }}
                    >
                      {getInitials(userData.name)}
                    </Avatar>
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        {userData.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {userData.email}
                      </Typography>
                    </Box>
                  </Box>
                  <IconButton
                    onClick={(e) => handleMenuOpen(e, userData)}
                    disabled={!hasPermission('manage_users')}
                  >
                    <MoreVert />
                  </IconButton>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Chip
                    icon={getRoleIcon(userData.role)}
                    label={getRoleLabel(userData.role)}
                    color={getRoleColor(userData.role)}
                    variant="outlined"
                  />
                  {userData.id === user?.id && (
                    <Chip
                      label="Vous"
                      color="primary"
                      size="small"
                    />
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Permissions Matrix */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Matrice des Permissions
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Aperçu des permissions accordées à chaque rôle d'utilisateur
          </Typography>
          
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell><strong>Permission</strong></TableCell>
                  <TableCell align="center"><strong>Employé</strong></TableCell>
                  <TableCell align="center"><strong>Administrateur</strong></TableCell>
                  <TableCell align="center"><strong>Super Admin</strong></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {[
                  'Voir le tableau de bord',
                  'Voir les produits',
                  'Gérer les produits',
                  'Supprimer les produits',
                  'Gérer les ventes',
                  'Voir les rapports',
                  'Voir les analyses',
                  'Gérer les utilisateurs',
                  'Modifier les enregistrements financiers',
                  'Paramètres système'
                ].map((permission) => (
                  <TableRow key={permission}>
                    <TableCell>{permission}</TableCell>
                    <TableCell align="center">
                      {permissions.employee.includes(permission) ? (
                        <Chip label="✓" color="success" size="small" />
                      ) : (
                        <Chip label="✗" color="default" size="small" />
                      )}
                    </TableCell>
                    <TableCell align="center">
                      {permissions.admin.includes(permission) ? (
                        <Chip label="✓" color="success" size="small" />
                      ) : (
                        <Chip label="✗" color="default" size="small" />
                      )}
                    </TableCell>
                    <TableCell align="center">
                      {permissions.superadmin.includes(permission) ? (
                        <Chip label="✓" color="success" size="small" />
                      ) : (
                        <Chip label="✗" color="default" size="small" />
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* User Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose} disabled>
          <Edit sx={{ mr: 1 }} fontSize="small" />
          Modifier
        </MenuItem>
        <MenuItem 
          onClick={handleMenuClose} 
          disabled={selectedUser?.id === user?.id}
          sx={{ color: 'error.main' }}
        >
          <Delete sx={{ mr: 1 }} fontSize="small" />
          Supprimer
        </MenuItem>
      </Menu>

      {/* Role Descriptions */}
      <Grid container spacing={3} sx={{ mt: 3 }}>
        <Grid item xs={12} md={4}>
          <Card variant="outlined">
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Work sx={{ mr: 1, color: 'info.main' }} />
                <Typography variant="h6">Employé</Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Accès de base pour effectuer des ventes et consulter les rapports. 
                Idéal pour le personnel de vente en magasin.
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card variant="outlined">
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AdminPanelSettings sx={{ mr: 1, color: 'warning.main' }} />
                <Typography variant="h6">Administrateur</Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Gestion complète des produits, ventes et utilisateurs. 
                Parfait pour les gérants de magasin.
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card variant="outlined">
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SupervisorAccount sx={{ mr: 1, color: 'error.main' }} />
                <Typography variant="h6">Super Administrateur</Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Accès complet incluant la suppression de produits et la modification 
                des enregistrements financiers. Réservé aux propriétaires.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Users;
